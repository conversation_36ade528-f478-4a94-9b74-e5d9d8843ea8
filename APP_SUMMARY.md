# Doctors App – Project Summary

## Overview

This application is a full-stack medical management system designed for clinics or small hospitals. It provides tools for doctors and staff to manage patients, appointments, treatments, visits, and prescriptions, with role-based access control and a modern web interface.

## Key Features

- **User Authentication & Roles:**
  - Supports roles: `admin`, `sudo`, `doctor`, `patient`.
  - Role-based access to routes and features.
- **Patient Management:**
  - Add, search, and manage patient records.
  - Each patient has a folder containing their medical history.
- **Appointment Scheduling:**
  - Doctors can create, update, and delete appointments.
  - Appointments are linked to patients and doctors.
- **Treatment & Visit Tracking:**
  - Create and update treatments for patients.
  - Log visits under treatments, with notes and dates.
- **Prescription Management:**
  - Add, update, and remove prescriptions for treatments.
  - Prescriptions are linked to medicines and can include dosage, description, and images.
- **Search & Reporting:**
  - Search for patients, treatments, and medicines by various criteria.
  - Generate reports (daily, monthly, custom) for visits and treatments.
- **Modern UI:**
  - Built with <PERSON><PERSON> and Tailwind CSS for a responsive, user-friendly experience.
  - Includes dark mode toggle and toast notifications.
- **API & Backend:**
  - RESTful API built with Express and MongoDB.
  - Secure endpoints with authentication and role-based middleware.

## Typical User Flow

1. **Login:** User logs in with email and password. Role is determined at login.
2. **Dashboard:** Doctor sees their dashboard with quick links to patients, appointments, and reports.
3. **Patient Management:** Doctor can add new patients or search for existing ones.
4. **Appointments:** Schedule, view, or update appointments for patients.
5. **Treatments & Visits:** For each patient, create treatments and log visits with notes and prescriptions.
6. **Prescriptions:** Add or remove prescriptions for a treatment, including medicine details.
7. **Reports:** Generate and view reports on visits and treatments.

## Technologies Used

- **Frontend:** React, Tailwind CSS, Vite
- **Backend:** Node.js, Express, MongoDB (Mongoose)
- **Other:** JWT for authentication, role-based middleware, RESTful API design

## Intended Audience

- Doctors, clinic staff, and administrators who need to manage patient care, appointments, and medical records efficiently.

---

_This summary provides AI and developers with the necessary context to understand the app’s structure, features, and user flows._
