import { useState, useCallback, useRef } from "react";
import { useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import {
  FLAGS,
  LoadingBarStore,
  NotBackLinkTo,
  PatientsDataStore,
  userDataStore,
  visitsExplorerStore,
} from "../data";
import {
  getPatientTreatementByFolderId,
  getTreatementVisitsByTreatementId,
  searchPatientByCINAPI,
} from "../api";
import toast from "react-hot-toast";
import { Store } from "react-data-stores";
export const useBacklink = (Link = "/") => {
  return useSearchParams()[0].get("backTo") || Link;
};

export function PreventBackBtn() {
  const navigate = useNavigate();

  useEffect(() => {
    // Push a dummy entry so back will stay on the page
    window.history.pushState(null, document.title, window.location.href);

    const onPopState = (event) => {
      // Push state again when back is pressed
      window.history.pushState(null, document.title, window.location.href);
      // Optionally, you can navigate somewhere else or show a warning here
      // Example: navigate("/somewhere");
    };

    window.addEventListener("popstate", onPopState);

    return () => {
      window.removeEventListener("popstate", onPopState);
    };
  }, [navigate]);

  return null; // This component doesn’t render anything
}

export function useFetchPatients() {
  const [loading, setLoading] = useState(false);
  const [Patients, setPatients] = PatientsDataStore.useStore({});

  // Load more patients for the given search term
  const loadMore = useCallback(
    async (search) => {
      if (loading || !search || search.length < 2) return;
      setLoading(true);
      LoadingBarStore.setCurrent({ loading: true });

      // Find or create paging info for this search term
      let pageEntry = FLAGS.loadeMore.patients.find((p) => p.value === search);
      if (!pageEntry) {
        pageEntry = { value: search, page: 0 };
        FLAGS.loadeMore.patients.push(pageEntry);
      } else {
        pageEntry.page++;
      }
      try {
        const [error, newPatients] = await searchPatientByCINAPI({
          cin: search,
          page_number: pageEntry.page,
          token: localStorage.getItem("token"),
        });

        if (!error && newPatients.length) {
          newPatients.forEach((newPatient) => {
            if (!Patients.patient.find((p) => p._id === newPatient._id)) {
              Patients.patient.push(newPatient);
            }
          });
          setPatients({ ...Patients });
        } else {
          pageEntry.page--; //! in case of not loaded due to any error or page not having any data
        }
      } catch (err) {
        console.error("Load more error:", err);
      } finally {
        setLoading(false);
        LoadingBarStore.setCurrent({ loading: false });
      }
    },
    [loading, Patients, setPatients, LoadingBarStore.setCurrent]
  );

  return { loading, loadMore };
}
export function useFetchPatientData() {
  const [Patients, setPatients] = PatientsDataStore.useStore({});

  const render = visitsExplorerStore.useStore({ setter: false });
  const userData = userDataStore.useStore({ setter: false });

  const load = useCallback(async () => {
    if (
      typeof render.patient !== "number" ||
      !Patients?.patient?.[render.patient]?.folder
    ) {
      return;
    }

    LoadingBarStore.setCurrent({ loading: true });
    const currentPatient = Patients.patient[render.patient];
    const currentFolder = currentPatient.folder;

    try {
      //! Handle treatment data fetching
      if (typeof render.treatement !== "number") {
        const treatments = currentFolder.treatments;

        if (
          !treatments ||
          treatments.length === 0 ||
          typeof treatments[0] === "string"
        ) {
          const [err, data] = await getPatientTreatementByFolderId(
            currentFolder._id,
            userData.token
          );

          if (err) throw err;

          currentPatient.folder.treatments = data;
          setPatients({ ...Patients });
        }
        return;
      }

      //! Handle visits data fetching
      const currentTreatment = currentFolder.treatments[render.treatement];
      if (!currentTreatment) return;

      const visits = currentTreatment.visits;
      if (!visits || visits.length === 0 || typeof visits[0] === "string") {
        const [err, data] = await getTreatementVisitsByTreatementId(
          currentTreatment._id,
          userData.token
        );

        if (err) throw err;

        currentTreatment.visits = data;
        setPatients({ ...Patients });
      }
    } catch (err) {
      console.error(err);
      const errorMessage =
        typeof render.treatement !== "number"
          ? `problem with patient: '${currentPatient.name.join(" ")}'`
          : `failed loading visits for treatement: "${
              currentFolder.treatments[render.treatement]?.title
            }"`;

      toast.error(errorMessage);
    } finally {
      LoadingBarStore.setCurrent({ loading: false });
    }
  }, [
    render,
    Patients,
    setPatients,
    LoadingBarStore.setCurrent,
    userData.token,
  ]);

  useEffect(() => {
    load();
  }, [render.treatement, render.patient, render.visit]);

  return null;
}
export function useRedirect() {
  const userData = userDataStore.useStore({ setter: false });
  useEffect(() => {
    if (!userData.token) {
      let return_link = "";
      console.log(window.location.pathname, NotBackLinkTo);
      if (!NotBackLinkTo.includes(location.pathname)) {
        return_link = "?backTo=" + encodeURIComponent(location.pathname);
      }
      return Store.navigateTo("/login" + return_link);
    }
  }, [userData.token]);
}
export const useDebounce = (delay = 300) => {
  const timerRef = useRef(undefined);
  const lastFunctionRef = useRef(undefined);

  const debouncer = useCallback(
    (fn = () => {}) => {
      if (timerRef.current) {
        lastFunctionRef.current = fn;
        return;
      }
      fn();
      timerRef.current = setTimeout(() => {
        timerRef.current = undefined;
        if (lastFunctionRef.current) {
          lastFunctionRef.current();
          lastFunctionRef.current = undefined;
        }
      }, delay);
    },
    [delay]
  );

  return debouncer;
};
