{"scripts": {"dev": "ts-node-dev --respawn index.ts", "generate-user": "ts-node-dev scripts/generate-user.ts", "generate-hospital": "ts-node-dev  scripts/generate-hospital.ts", "build": "tsc", "start": "node dist/index.js", "refresh-env": "npx ts-node-dev scripts/refresh-env.ts", "script-list": "npx ts-node-dev scripts/script-list.ts"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/node": "^22.15.31", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2"}}