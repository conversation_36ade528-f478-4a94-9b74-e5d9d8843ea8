import { Route, Routes, useNavigate } from "react-router-dom";
import "./App.css";
import Login from "./Routes/Login";
import SideBar from "./SideBar";
import { PopUpElement } from "./data";
import { Store } from "react-data-stores";
import { _404, IndexRoute } from "./Routes";
import DoctorRout<PERSON> from "./Routes/Doctor";
import LoadingBar from "./utils/LoadingBar";
import { Toaster } from "react-hot-toast";
import { PreventBackBtn, useRedirect } from "./utils/costumeHook";

export default function App() {
  const PopUp = PopUpElement.useStore({ setter: false });
  Store.navigateTo = useNavigate();
  useRedirect();
  return (
    <>
      <SideBar />
      <PreventBackBtn />
      <div className="">
        <Routes>
          <Route index element={<IndexRoute />} />
          <Route path="/login" element={<Login />} />
          <Route path="/doctor/*" element={<DoctorRoute />} />
          <Route path="*" element={<_404 />} />
        </Routes>
      </div>
      <PopUp.element />
      <LoadingBar />
      <Toaster position="top-right" />
    </>
  );
}
