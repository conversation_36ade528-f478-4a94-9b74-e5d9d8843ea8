import { InputAdornment, TextField } from "@mui/material";
import { WorkersExplorerStore } from "../../../../data";
import { useState } from "react";

import Render from "../_components/Rendrer";

import { useDebounce, useFetchPatients } from "../../../../utils/costumeHook";

export default function Explorer({}) {
  const WorkersExplorerState = WorkersExplorerStore.useStore({ setter: false });
  const [search, setSearch] = useState("");
  const { loadMore } = useFetchPatients();
  const debouncer = useDebounce(300);

  return (
    <div className="w-[30%] min-w-[120px] border-r border-gray-200 h-full p-3 bg-gray-50 flex flex-col justify-center">
      <h3 className="text-lg font-semibold text-gray-700 mb-3 flex items-center truncate">
        <i className="fa-solid fa-folder mr-2 text-blue-500"></i>
        {typeof WorkersExplorerState.worker === "number"
          ? "workers"
          : "category"}
      </h3>

      <div className="flex gap-2">
        <TextField
          variant="outlined"
          size="small"
          placeholder="Search..."
          fullWidth
          value={search}
          onChange={(e) => {
            setSearch(e.target.value);
            const search = e.target.value;
            debouncer(() => {
              loadMore(search);
            });
          }}
          className="mb-3"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <i className="fa-solid fa-magnifying-glass text-gray-400"></i>
              </InputAdornment>
            ),
            className: "bg-white lack",
          }}
        />
      </div>
      <ul className="flex-1 overflow-y-auto space-y-1 mt-3">
        <Render search={search} setSearch={setSearch} />
      </ul>
    </div>
  );
}
