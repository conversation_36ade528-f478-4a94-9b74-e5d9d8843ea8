import { WorkersData, WorkersExplorerStore } from "../../../../data";

export default function Render({ search, setSearch = () => {} }) {
  const [render, setRendrer] = WorkersExplorerStore.useStore();
  const Workers = WorkersData.useStore({ setter: false });
  if (typeof render.type === "string") {
    console.log(Workers);
    return (
      // <ShowWorkersList
      //   searchInput={search}
      //   Workers={Workers[render.type]}
      //   onSelect={(i) => {
      //     setSearch("");
      //     console.log(i)
      //   }}
      // />
      <>not inplemented yet (list of workers)</>
    );
  }
}
