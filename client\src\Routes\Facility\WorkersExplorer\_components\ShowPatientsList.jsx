export default function ShowPatientsList({
  patients = [],
  onSelect = () => {},
  searchInput,
}) {
  return (
    <>
      {patients.map((patient, i) => {
        if (
          !patient.name
            .join(" ")
            .toLowerCase()
            .includes(searchInput.toLowerCase()) &&
          !patient.cin.toLowerCase().includes(searchInput.toLowerCase())
        )
          return null;
        return (
          <li
            key={patient._id}
            onClick={() => onSelect(i, patient._id)}
            className="px-3 py-2 cursor-pointer hover:bg-blue-50 rounded-md transition-colors
                    text-gray-700 hover:text-blue-600 border border-transparent hover:border-blue-100"
          >
            <div className="flex items-center min-w-0">
              <i className="fa-solid fa-user mr-2 text-blue-400 flex-shrink-0"></i>
              <div className="min-w-0">
                <div className="truncate">{patient.name.join(" ")}</div>
                <div className="text-xs text-gray-500 truncate">
                  {patient.cin}
                </div>
              </div>
            </div>
          </li>
        );
      })}
    </>
  );
}
