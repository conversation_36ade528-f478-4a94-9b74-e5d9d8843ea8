import { Schema, model, Types, Model } from "mongoose";
import { BaseModel } from "./BaseModel";
import { ModelRefs } from "./ModelRefs";

interface IPrescription {
  medicin: Types.ObjectId;
  dose: string;
  date: number;
}

export interface ITreatment extends BaseModel {
  folder: Types.ObjectId;
  patient: Types.ObjectId;
  visits: Types.ObjectId[];
  tests: Types.ObjectId[];
  doctor: Types.ObjectId;
  start: number;
  end: number | null;
  prescriptions: IPrescription[];
  state: "O" | "C" | "T"; // O = ongoing, C = canceled, T = terminated
  title: string;
  description: string;
  is_deleted?: boolean;
}
interface TreatmentModel extends Model<ITreatment> {}

const prescriptionSchema = new Schema<IPrescription>(
  {
    medicin: {
      type: Schema.Types.ObjectId,
      ref: ModelRefs.Medicin.modelName,
      required: true,
    },
    dose: { type: String, required: true },
    date: { type: Number, required: true, default: Date.now },
  },
  { _id: false }
);

const treatmentSchema = new Schema<ITreatment>({
  folder: {
    type: Schema.Types.ObjectId,
    ref: ModelRefs.Folder.modelName,
    required: true,
  },
  patient: {
    type: Schema.Types.ObjectId,
    ref: ModelRefs.User.modelName,
    required: true,
  },
  doctor: {
    type: Schema.Types.ObjectId,
    ref: ModelRefs.User.modelName,
    required: true,
  },
  visits: [{ type: Schema.Types.ObjectId, ref: ModelRefs.Visit.modelName }],
  tests: [{ type: Schema.Types.ObjectId, ref: ModelRefs.Test.modelName }],
  start: { type: Number, required: true },
  end: { type: Number, default: null },
  prescriptions: [prescriptionSchema],
  state: { type: String, enum: ["O", "C", "T"], default: "O" },
  title: { type: String, required: true, unique: true },
  description: { type: String, default: "" },
  createdAt: Number,
  updatedAt: Number,
  is_deleted: { type: Boolean, default: false },
});

export const Treatment = model<ITreatment, TreatmentModel>(
  ModelRefs.Treatment.modelName,
  treatmentSchema
);
