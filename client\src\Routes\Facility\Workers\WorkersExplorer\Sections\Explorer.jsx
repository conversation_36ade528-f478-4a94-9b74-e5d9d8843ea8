import { InputAdornment, TextField } from "@mui/material";
import {
  LoadingBarStore,
  WorkersDataStore,
  PopUpElement,
  workersExplorerStore,
} from "../../../../../data";
import { useEffect, useState } from "react";

import LoadMore from "../_components/LoadMore";
import Render from "../_components/Renderer";
import ContainerPopUp from "../../../../../utils/ContainerPopUp";
import WorkerAddForm from "../_components/WorkerAddForm";
import {
  useDebounce,
  useFetchWorkers,
} from "../../../../../utils/costumeHook";
import WorkersAdvancedSearch from "../_components/WorkersAdvancedSearch";
import { searchWorkerViaAdvancedOptionsAPI } from "../../../../../api";
import toast from "react-hot-toast";

export default function Explorer({}) {
  const workersExplorerState = workersExplorerStore.useStore({ setter: false });
  const [search, setSearch] = useState("");
  const { loadMore } = useFetchWorkers();
  const debouncer = useDebounce(300);
  
  async function AdvancedWorkerSearch(
    e = {
      cin: "",
      name: "",
      phone: "",
      email: "",
      role: "",
      specialization: "",
      searchType: "exact", // 'exact' or 'partial'
    }
  ) {
    LoadingBarStore.setCurrent({ loading: true });
    const [err, data] = await searchWorkerViaAdvancedOptionsAPI(
      localStorage.getItem("token"),
      {
        ...e,
        exact: e.searchType == "exact" ? "true" : undefined,
      }
    );
    if (err) {
      console.log("err:", err);
      toast.error(err?.response?.data?.error || "error searching for worker");
      LoadingBarStore.setCurrent({ loading: false });
      return;
    }
    const workers = WorkersDataStore.getCurrent().worker;
    const skip = [];
    workers.forEach((worker) => {
      const index = data.findIndex((data) => data._id == worker._id);
      if (index >= 0) {
        skip.push(index);
      }
      return;
    });
    data.forEach((data, i) => {
      if (skip.includes(i)) return;
      workers.push(data);
    });

    WorkersDataStore.setCurrent({ ...WorkersDataStore.getCurrent() });
    LoadingBarStore.setCurrent({ loading: false });
    PopUpElement.setCurrent({ element: () => null });
    if (e.cin) {
      setSearch(e.cin);
    }
  }

  return (
    <div className="w-[30%] min-w-[120px] border-r border-gray-200 h-full p-3 bg-gray-50 flex flex-col justify-center">
      <h3 className="text-lg font-semibold text-gray-700 mb-3 flex items-center truncate">
        <i className="fa-solid fa-users mr-2 text-blue-500"></i>
        {typeof workersExplorerState.worker === "number"
          ? "Worker Details"
          : "Workers"}
      </h3>

      <div className="flex gap-2">
        <TextField
          variant="outlined"
          size="small"
          placeholder="Search workers..."
          fullWidth
          value={search}
          onChange={(e) => {
            setSearch(e.target.value);
            const search = e.target.value;
            if (search.includes("@")) {
              //enable advanced search
              PopUpElement.setCurrent({
                element: () => (
                  <ContainerPopUp>
                    <WorkersAdvancedSearch onSearch={AdvancedWorkerSearch} />
                  </ContainerPopUp>
                ),
              });
              setSearch("");
              return;
            }
            debouncer(() => {
              loadMore(search);
            });
          }}
          className="mb-3"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <i className="fa-solid fa-magnifying-glass text-gray-400"></i>
              </InputAdornment>
            ),
            className: "bg-white lack",
          }}
        />
        <button
          className="bg-blue-50 border-blue-100 border-1 rounded-[5px] text-blue-700 w-9"
          onClick={() => {
            PopUpElement.setCurrent({
              element: () => (
                <ContainerPopUp>
                  <WorkerAddForm />
                </ContainerPopUp>
              ),
            });
          }}
        >
          +
        </button>
      </div>
      <ul className="flex-1 overflow-y-auto space-y-1 mt-3">
        <Render search={search} setSearch={setSearch} />
        <LoadMore search={search} />
      </ul>
    </div>
  );
}
