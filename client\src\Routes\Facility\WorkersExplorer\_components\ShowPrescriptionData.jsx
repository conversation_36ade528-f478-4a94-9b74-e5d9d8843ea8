import { <PERSON>, <PERSON>lapse, Divider, <PERSON><PERSON><PERSON><PERSON>on, Typography } from "@mui/material";
import { useState } from "react";
import {
  LocalHospital,
  Medication,
  CalendarToday,
  Description,
  ExpandMore,
  ExpandLess,
} from "@mui/icons-material";
import { formatDate } from "../../../../utils/dateFormater";
import {
  LoadingBarStore,
  PatientsDataStore,
  PopUpElement,
  userDataStore,
  visitsExplorerStore,
} from "../../../../data";
import ContainerPopUp from "../../../../utils/ContainerPopUp";
import { updateTreatementPrescriptionAPI } from "../../../../api";
import toast from "react-hot-toast";

const DetailItem = ({ icon, label, value }) => (
  <Box sx={{ display: "flex", alignItems: "center", mb: 1.5 }}>
    <Box sx={{ color: "primary.main", mr: 1.5 }}>{icon}</Box>
    <Box>
      <Typography
        variant="subtitle2"
        color="text.secondary"
        sx={{ fontSize: "0.75rem" }}
      >
        {label}
      </Typography>
      <Typography variant="body1" sx={{ fontWeight: 500 }}>
        {value}
      </Typography>
    </Box>
  </Box>
);

export default function ShowPrescriptionData({ treatment }) {
  const [sectionExpanded, setSectionExpanded] = useState(true);
  const [expandedPrescriptions, setExpandedPrescriptions] = useState({});
  const prescriptions = treatment.prescriptions || [];

  const togglePrescription = (index) => {
    setExpandedPrescriptions((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };
  const HandleDeletion = async (prescription, index) => {
    if (!prescription) return;
    LoadingBarStore.setCurrent({ loading: true });
    //! FILTER PRESCRIPTION FOR DELETETION by `._id`
    const newPrescriptionArry = prescriptions.filter(
      (e) => e.medicin._id != prescription.medicin._id
    );
    const [err, data] = await updateTreatementPrescriptionAPI(
      treatment._id,
      {
        prescriptions: newPrescriptionArry
          //! REMAP THE PRESCRIPTION ARRAY TO MATCH THE INITIAL TREATEMENT(API SPECEFIC)
          .map((p) => {
            return {
              ...p,
              medicin: p.medicin._id,
            };
          }),
      },
      userDataStore.getCurrent().token
    );
    togglePrescription(index);
    if (err) {
      LoadingBarStore.setCurrent({ loading: true });
      console.log(err);
      toast.error("unable to delete prescription");
      return;
    }
    //!update Internal state
    const currentAPPState = visitsExplorerStore.getCurrent();
    PatientsDataStore.getCurrent().patient[
      currentAPPState.patient
    ].folder.treatments[currentAPPState.treatement].prescriptions =
      newPrescriptionArry;
    //!triger ui re render
    PatientsDataStore.setCurrent({
      ...PatientsDataStore.getCurrent(),
    });
    toast.success("prescriptiont deleted successfully");
    LoadingBarStore.setCurrent({ loading: false });
  };
  return (
    <Box sx={{ mt: 4 }}>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          cursor: "pointer",
          mb: 2,
        }}
        onClick={() => setSectionExpanded(!sectionExpanded)}
      >
        <Typography
          variant="h5"
          sx={{
            fontWeight: 600,
            display: "flex",
            alignItems: "center",
            gap: 1,
          }}
        >
          <LocalHospital color="primary" />
          Prescriptions ({treatment.prescriptions.length})
        </Typography>
        <Box>
          <IconButton size="small">
            {sectionExpanded ? <ExpandLess /> : <ExpandMore />}
          </IconButton>{" "}
          <button
            className="text-blue-500 hover:text-blue-600"
            onClick={() => {
              PopUpElement.setCurrent({
                element: () => (
                  <ContainerPopUp>
                    <AddPrescription />
                  </ContainerPopUp>
                ),
              });
            }}
          >
            <i className="fa-solid fa-prescription-bottle-medical"></i>
          </button>
        </Box>
      </Box>

      <Collapse in={sectionExpanded}>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          {prescriptions.map((prescription, index) => (
            <Box
              key={index}
              sx={{
                borderRadius: "12px",
                bgcolor: "background.paper",
                boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  p: 2,
                  cursor: "pointer",
                }}
                onClick={() => togglePrescription(index)}
              >
                <Typography sx={{ fontWeight: 500 }}>
                  {prescription.medicin.title} - {formatDate(prescription.date)}
                </Typography>
                <div>
                  <IconButton size="small">
                    {expandedPrescriptions[index] ? (
                      <ExpandLess />
                    ) : (
                      <ExpandMore />
                    )}
                  </IconButton>
                  <span onClick={() => HandleDeletion(prescription, index)}>
                    <i className="fa-solid fa-trash-can text-red-600   hover:text-shadow-[0_0_15px_#ff0000a6]"></i>
                  </span>
                </div>
              </Box>

              <Collapse in={expandedPrescriptions[index]}>
                <Box sx={{ p: 3, pt: 0 }}>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: { xs: "column", sm: "row" },
                      gap: 3,
                      mb: 2,
                    }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <DetailItem
                        icon={<Medication fontSize="small" />}
                        label="MEDICATION"
                        value={prescription.medicin.title}
                      />
                      <DetailItem
                        icon={<Medication fontSize="small" />}
                        label="DOSAGE"
                        value={prescription.dose}
                      />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <DetailItem
                        icon={<CalendarToday fontSize="small" />}
                        label="PRESCRIBED ON"
                        value={formatDate(prescription.date)}
                      />
                    </Box>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Box>
                    <DetailItem
                      icon={<Description fontSize="small" />}
                      label="DESCRIPTION"
                      value={prescription.medicin.description}
                    />
                  </Box>

                  {prescription.medicin.img && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary">
                        MEDICATION IMAGE:
                      </Typography>
                      <Box
                        component="img"
                        src={prescription.medicin.img}
                        alt={prescription.medicin.title}
                        sx={{
                          width: 120,
                          height: 120,
                          objectFit: "contain",
                          border: "1px solid #e0e0e0",
                          borderRadius: "8px",
                          mt: 1,
                        }}
                        onClick={() => {
                          PopUpElement.setCurrent({
                            element: () => {
                              return (
                                <ContainerPopUp>
                                  <div className="bg-white lack rounded-lg shadow-xl overflow-hidden w-full max-w-md mx-4">
                                    <div className="p-6 space-y-4">
                                      <h1 className="text-2xl font-bold text-gray-800 text-center">
                                        {prescription.medicin.title}
                                      </h1>

                                      <div className="flex justify-center items-center">
                                        <div className="relative w-full max-w-xs h-64 md:h-80 aspect-square  rounded-lg overflow-hidden border border-gray-200">
                                          <img
                                            className="w-full h-full object-contain p-4"
                                            src={prescription.medicin.img}
                                            alt={prescription.medicin.title}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </ContainerPopUp>
                              );
                            },
                          });
                        }}
                      />
                    </Box>
                  )}
                </Box>
              </Collapse>
            </Box>
          ))}
        </Box>
      </Collapse>
    </Box>
  );
}
