import { WorkersDataStore, workersExplorerStore } from "../../../../../data";
import ShowWorkersList from "./ShowWorkersList";

export default function Renderer({ search, setSearch = () => {} }) {
  const [render, setRenderer] = workersExplorerStore.useStore();
  const workers = WorkersDataStore.useStore({ setter: false }).worker;
  
  if (!render || typeof render.worker !== "number") {
    return (
      <ShowWorkersList
        searchInput={search}
        workers={workers}
        onSelect={(i) => {
          setSearch("");
          setRenderer({ worker: i });
        }}
      />
    );
  }
  
  // If a worker is selected, show their details in the content area
  return null;
}
