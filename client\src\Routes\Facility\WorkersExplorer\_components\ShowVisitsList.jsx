export default function ShowVisitsList({
  visits = [],
  onSelect = () => {},
  searchInput,
}) {
  return (
    <>
      {visits.map((visit, i) => {
        if (!visit.notes.toLowerCase().includes(searchInput.toLowerCase()))
          return null;
        return (
          <li
            key={visit._id}
            onClick={() => onSelect(i, visit)}
            className="px-3 py-2 cursor-pointer hover:bg-blue-50 rounded-md transition-colors
                    text-gray-700 hover:text-blue-600 border border-transparent hover:border-blue-100"
          >
            <div className="flex items-start min-w-0">
              <i className="fa-solid fa-notes-medical mt-1 mr-2 text-blue-400 flex-shrink-0"></i>
              <div className="min-w-0">
                <div className="text-sm font-medium truncate">
                  {new Date(visit.date).toLocaleDateString()}
                </div>
                <div className="text-sm text-gray-500 truncate">
                  {visit.notes || "No notes available"}
                </div>
              </div>
            </div>
          </li>
        );
      })}
    </>
  );
}
