import { Router } from "express";
import { authMiddleware } from "../../middlewares/auth";
import { forceQuitMiddleware } from "../../middlewares/forceQuite";
import { User } from "../../Models/User";
import { roles } from "../../utils/roles";
import { QUERY_PAGE_SIZE_LIMIT } from "../../utils/FLAGS";

const router = Router();
router.use(authMiddleware);

// GET /facility/workers - Get workers (admin and doctor roles) for the facility
router.get(
  "/workers",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const { page = 0, search = "", exact = "false" } = req.query;
      const pageNumber = parseInt(page as string);
      const isExact = exact === "true";

      // Get the facility manager's hospital from their profile
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );

      if (!facilityManagerProfile || !facilityManagerProfile.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital;

      // Build search query for workers (admin and doctor roles only)
      const searchQuery: any = {
        is_deleted: { $ne: true },
        "profiles.type": { $in: [roles.admin, roles.doctor] },
        "profiles.hospital": facilityId
      };

      if (search) {
        const searchStr = search as string;
        if (isExact) {
          searchQuery.$or = [
            { cin: searchStr },
            { email: searchStr },
            { phone: searchStr }
          ];
        } else {
          searchQuery.$or = [
            { name: { $regex: searchStr, $options: "i" } },
            { cin: { $regex: searchStr, $options: "i" } },
            { email: { $regex: searchStr, $options: "i" } },
            { phone: { $regex: searchStr, $options: "i" } }
          ];
        }
      }

      const workers = await User.find(searchQuery)
        .skip(pageNumber * QUERY_PAGE_SIZE_LIMIT)
        .limit(QUERY_PAGE_SIZE_LIMIT)
        .select("-password")
        .lean();

      res.json(workers);
    } catch (error) {
      console.error("Error fetching workers:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// GET /facility/workers/search - Advanced search for workers
router.get(
  "/workers/search",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const { cin, name, phone, email, role, specialization, exact = "false" } = req.query;
      const isExact = exact === "true";

      // Get the facility manager's hospital
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );

      if (!facilityManagerProfile || !facilityManagerProfile.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital;

      const searchQuery: any = {
        is_deleted: { $ne: true },
        "profiles.type": { $in: [roles.admin, roles.doctor] },
        "profiles.hospital": facilityId
      };

      if (cin) {
        searchQuery.cin = isExact ? cin : { $regex: cin, $options: "i" };
      }
      if (name) {
        searchQuery.name = isExact ? name : { $regex: name, $options: "i" };
      }
      if (phone) {
        searchQuery.phone = isExact ? phone : { $regex: phone, $options: "i" };
      }
      if (email) {
        searchQuery.email = isExact ? email : { $regex: email, $options: "i" };
      }
      if (role && [roles.admin, roles.doctor].includes(role as string)) {
        searchQuery["profiles.type"] = role;
      }
      if (specialization) {
        searchQuery["profiles.specialization"] = isExact
          ? specialization
          : { $regex: specialization, $options: "i" };
      }

      const workers = await User.find(searchQuery)
        .select("-password")
        .lean();

      res.json(workers);
    } catch (error) {
      console.error("Error in advanced worker search:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// POST /facility/workers - Create a new worker (admin or doctor)
router.post(
  "/workers",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
    sudo: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const {
        cin,
        firstName,
        lastName,
        email,
        phone,
        gender,
        birthDate,
        role,
        specialization,
        password
      } = req.body;

      // Validate required fields
      if (!cin || !firstName || !lastName || !email || !gender || !birthDate || !role || !password) {
        res.status(400).json({ error: "Missing required fields" });
        return;
      }

      // Validate role
      if (![roles.admin, roles.doctor].includes(role)) {
        res.status(400).json({ error: "Invalid role. Only admin and doctor roles are allowed" });
        return;
      }

      // Get the facility manager's hospital
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );

      if (!facilityManagerProfile || !facilityManagerProfile.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital;

      // Check if user with this CIN or email already exists
      const existingUser = await User.findOne({
        $or: [{ cin }, { email }],
        is_deleted: { $ne: true }
      });

      if (existingUser) {
        if (existingUser.profiles.find(p => p.type === role)) {
          res.status(400).json({ error: "User already exists with this role" });
          return;
        }
        existingUser.profiles.push({
          type: role,
          hospital: facilityId,
          specialization: role === roles.doctor ? (specialization || "") : "",
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });  
        await existingUser.save();
        res.status(203).json(existingUser);
        return;
      }

      // Create new user
      const newUser = new User({
        name: [firstName, lastName],
        email,
        phone: phone || null,
        img: null,
        gender,
        BirthDay: typeof birthDate === 'number' ? birthDate : new Date(birthDate).getTime(),
        password: require("../../utils/passwordHash").hashPassword(password),
        cin,
        profiles: [{
          type: role,
          hospital: facilityId,
          specialization: role === roles.doctor ? (specialization || "") : "",
          createdAt: Date.now(),
          updatedAt: Date.now(),
        }],
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      await newUser.save();

      // Return the created user without password
      const userResponse = newUser.toObject();
      const { password: _, ...userWithoutPassword } = userResponse;

      res.status(201).json(userWithoutPassword);
    } catch (error) {
      console.error("Error creating worker:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// DELETE /facility/workers/:workerId - Soft delete a worker
router.delete(
  "/workers/:workerId",
  forceQuitMiddleware({
    admin: { HttpCode: 401, reason: "You cannot access this resource" },
    patient: { HttpCode: 401, reason: "You cannot access this resource" },
    doctor: { HttpCode: 401, reason: "You cannot access this resource" },
    sudo: { HttpCode: 401, reason: "You cannot access this resource" },
  }),
  async (req, res) => {
    try {
      const { workerId } = req.params;

      if (!workerId) {
        res.status(400).json({ error: "Worker ID is required" });
        return;
      }

      // Get the facility manager's hospital
      const facilityManagerUser = await User.findById(req.user?.id);
      if (!facilityManagerUser) {
        res.status(404).json({ error: "User not found" });
        return;
      }

      const facilityManagerProfile = facilityManagerUser.profiles.find(
        p => p.type === roles.facilityManager
      );

      if (!facilityManagerProfile || !facilityManagerProfile.hospital) {
        res.status(400).json({ error: "Facility manager not associated with a facility" });
        return;
      }

      const facilityId = facilityManagerProfile.hospital;

      // Find the worker and verify they belong to this facility
      const worker = await User.findById(workerId);
      if (!worker) {
        res.status(404).json({ error: "Worker not found" });
        return;
      }

      // Check if worker belongs to this facility and has admin/doctor role
      const workerProfile = worker.profiles.findIndex(
        p => (p.type === roles.admin || p.type === roles.doctor) &&
            p.hospital?.toString() === facilityId.toString()
      );

      if (workerProfile === -1) {
        res.status(403).json({ error: "Worker does not belong to your facility or is not a valid worker" });
        return;
      }

      worker.profiles.splice(workerProfile, 1);
      worker.updatedAt = Date.now();
      await worker.save();

      res.json({ message: "Worker deleted successfully" });
    } catch (error) {
      console.error("Error deleting worker:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

router.post("/", async (_req, res) => {
  res.status(501).json({ message: "Not implemented" });
});
export default router;
