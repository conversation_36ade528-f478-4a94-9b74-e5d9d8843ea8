import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogContent, DialogActions } from "@mui/material";
import { roles, WorkersDataStore, LoadingBarStore } from "../../../../../data";
import { formatDate } from "../../../../../utils/dateFormater";
import { deleteWorkerAPI } from "../../../../../api";
import toast from "react-hot-toast";

export default function ShowWorkerData({ worker }) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  if (!worker) return null;
  console.log(worker);
  const workerProfile = worker.profiles.find(p => p.type === roles["admin"] || p.type === roles["doctor"]);
  const roleDisplay = workerProfile?.type === roles["doctor"] ? "Doctor" : "Admin";
  const specialization = workerProfile?.specialization || "Not specified";

  const birthDate = worker.BirthDay ? formatDate(worker.BirthDay) : "Not specified";

  const handleDeleteWorker = async () => {
    LoadingBarStore.setCurrent({ loading: true });

    try {
      const [error] = await deleteWorkerAPI(
        localStorage.getItem("token"),
        worker._id
      );

      if (error) {
        toast.error(error?.response?.data?.error || "Failed to delete worker");
        return;
      }

      // Remove the worker from the store
      const currentWorkers = WorkersDataStore.getCurrent();
      currentWorkers.worker = currentWorkers.worker.filter(w => w._id !== worker._id);
      WorkersDataStore.setCurrent({ ...currentWorkers });

      toast.success("Worker deleted successfully!");
      setDeleteDialogOpen(false);
    } catch (err) {
      console.error("Error deleting worker:", err);
      toast.error("Failed to delete worker");
    } finally {
      LoadingBarStore.setCurrent({ loading: false });
    }
  };
  const genderData = {
    M: { icon: "mars", color: "blue-600", label: "Male" },
    F: { icon: "venus", color: "pink-600", label: "Female" },
  };

  const { icon, color, label } = worker.gender 
    ? genderData[worker.gender] 
    : { icon: "genderless", color: "gray-500", label: "Not specified" };

  const roleData = {
  
  doctor: { icon: "user-doctor", color: "blue-600", bgColor: "blue-100" },
  admin: { icon: "user-tie", color: "indigo-600", bgColor: "indigo-100" }

  };

  const roleIcon = workerProfile?.type 
    ? roleData[workerProfile.type]?.icon || "user"
    : "user";

  const roleColor = workerProfile?.type 
    ? roleData[workerProfile.type]?.color || "gray-600"
    : "gray-600";

  return (
    <div className="pb-4">
      <h2 className="text-2xl font-bold text-gray-900 mb-3 flex items-center">
        <i className={`fas fa-${roleIcon} text-${roleColor} mr-2`}></i>
        {roleDisplay}:{" "}
        <span className="text-blue-700 ml-1">{worker.name.join(" ")}</span>
      </h2>

      <div className="bg-gray-100 p-4 rounded-lg border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Role */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="fas fa-user-tag text-gray-500 mr-2"></i>
              Role:
            </h4>
            <p className="text-gray-900 flex items-center">
              <i className={`fas fa-${roleIcon} text-${roleColor} mr-2`}></i>
              {roleDisplay}
              {workerProfile?.type === "doctor" && specialization !== "Not specified" && (
                <span className="ml-2 text-sm text-gray-600">({specialization})</span>
              )}
            </p>
          </div>

          {/* CIN */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="fas fa-id-card text-gray-500 mr-2"></i>
              CIN:
            </h4>
            <p className="text-gray-900">
              {worker.cin ? (
                worker.cin
              ) : (
                <span className="flex items-center">
                  <i className="fas fa-circle-question text-gray-500 mr-2"></i>
                  Not specified
                </span>
              )}
            </p>
          </div>

          {/* Gender */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="fas fa-venus-mars text-gray-500 mr-2"></i>
              Gender:
            </h4>
            <p className="text-gray-900 flex items-center">
              <i className={`fas fa-${icon} text-${color} mr-2`}></i>
              {label}
            </p>
          </div>

          {/* Date of Birth */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="fas fa-birthday-cake text-gray-500 mr-2"></i>
              Date of Birth:
            </h4>
            <p className="text-gray-900">
              {birthDate === "Not specified" ? (
                <span className="flex items-center">
                  <i className="fas fa-circle-question text-gray-500 mr-2"></i>
                  {birthDate}
                </span>
              ) : (
                birthDate
              )}
            </p>
          </div>

          {/* Email */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="fas fa-envelope text-gray-500 mr-2"></i>
              Email:
            </h4>
            <p className="text-gray-900">
              {worker.email ? (
                worker.email
              ) : (
                <span className="flex items-center">
                  <i className="fas fa-circle-question text-gray-500 mr-2"></i>
                  Not specified
                </span>
              )}
            </p>
          </div>

          {/* Phone */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="fas fa-phone text-gray-500 mr-2"></i>
              Phone:
            </h4>
            <p className="text-gray-900">
              {worker.phone ? (
                worker.phone
              ) : (
                <span className="flex items-center">
                  <i className="fas fa-circle-question text-gray-500 mr-2"></i>
                  Not provided
                </span>
              )}
            </p>
          </div>

          {/* Account Created */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="fas fa-calendar-plus text-gray-500 mr-2"></i>
              Profile Created:
            </h4>
            <p className="text-gray-900">
              {formatDate(workerProfile?.createdAt || worker.createdAt)}
            </p>
          </div>

          {/* Last Updated */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="fas fa-calendar-check text-gray-500 mr-2"></i>
              Last Updated:
            </h4>
            <p className="text-gray-900">
              {formatDate(workerProfile?.updatedAt || worker.updatedAt)}
            </p>
          </div>
        </div>
      </div>

      {/* Delete Button */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <Button
          variant="outlined"
          color="error"
          onClick={() => setDeleteDialogOpen(true)}
          startIcon={<i className="fas fa-trash"></i>}
          className="w-full"
        >
          Delete Worker
        </Button>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Confirm Delete Worker
        </DialogTitle>
        <DialogContent>
          <p>
            Are you sure you want to delete <strong>{worker.name.join(" ")}</strong>?
          </p>
          <p className="text-gray-600 mt-2">
            This action will soft delete the worker from your facility. The worker's data will be preserved but they will no longer have access to the system.
          </p>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setDeleteDialogOpen(false)}
            color="primary"
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteWorker}
            color="error"
            variant="contained"
          >
            Delete Worker
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}