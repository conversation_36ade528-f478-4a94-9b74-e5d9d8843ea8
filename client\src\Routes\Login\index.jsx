import { useEffect, useState } from "react";
import {
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Paper,
  Typography,
  Box,
  CircularProgress,
  Alert,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { Store } from "react-data-stores";
import { loginAPI } from "../../api";
import { LoadingBarStore, userDataStore, UserToken } from "../../data";
import { useBacklink } from "../../utils/costumeHook";

export default () => {
  const [showPass, setShowPass] = useState(false);
  const [form, setForm] = useState({
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const setUserData = userDataStore.useStore({ getter: false });
  const setLoadingBarFlag = LoadingBarStore.useStore({ getter: false });
  const backToLink = useBacklink("/");

  useEffect(() => {
    const token = localStorage.getItem(UserToken);
    if (!token) return;
    //TODO: connect using the token
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setLoadingBarFlag({ loading: true });

    try {
      const res = await loginAPI({ ...form });

      if (!res) {
        setError("Invalid credentials. Please try again.");
        return;
      }

      if (res.data.data.cin == form.password) {
        alert("Please change your default password");
      }
      if (res.data.data.profiles.length > 1) {
        alert("more than 1 profile0");
        return;
      }
      res.data.data.role = res.data.data.profiles[0].type;
      res.data.data.hospital = res.data.data.profiles[0].hospital;
      res.data.data.specialization = res.data.data.profiles[0].specialization;

      setUserData({ token: res.data.tokens[0], data: res.data.data });
      localStorage.setItem(UserToken, res.data.tokens[0]);
      Store.navigateTo(backToLink);
    } catch (err) {
      console.log("err", err);
      setError("Login failed. Please check your credentials.");
    } finally {
      setIsLoading(false);
      setLoadingBarFlag({ loading: false });
    }
  };

  return (
    <Box className="flex justify-center items-center min-h-screen bg-gradient-to-br from-gray-50 to-gray-200 p-4">
      <Paper
        elevation={3}
        className="w-full max-w-md p-8 rounded-lg text-center"
      >
        <Box className="mb-8 flex flex-col items-center">
          <Box className="w-20 h-20 mb-4 rounded-full bg-blue-600 flex items-center justify-center text-white text-2xl font-bold">
            LOGO
          </Box>

          <Typography
            variant="h4"
            component="h1"
            className="mb-2 font-semibold"
          >
            Welcome Back
          </Typography>

          <Typography color="text.secondary">
            Please sign in to continue
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" className="mb-4">
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit} className="mt-6">
          <TextField
            fullWidth
            margin="normal"
            label="Email Address"
            type="email"
            variant="outlined"
            value={form.email}
            onChange={(e) => setForm({ ...form, email: e.target.value })}
            required
            className="rounded"
          />

          <TextField
            fullWidth
            margin="normal"
            label="Password"
            type={showPass ? "text" : "password"}
            variant="outlined"
            value={form.password}
            onChange={(e) => setForm({ ...form, password: e.target.value })}
            required
            className="rounded"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={() => setShowPass(!showPass)}
                    edge="end"
                  >
                    {showPass ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <Button
            fullWidth
            variant="contained"
            size="large"
            type="submit"
            disabled={isLoading}
            className="mt-6 mb-4 py-3 rounded text-base normal-case"
          >
            {isLoading ? (
              <Box className="flex gap-2 items-center">
                Sign In
                <CircularProgress size={24} color="inherit" />
              </Box>
            ) : (
              "Sign In"
            )}
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};
