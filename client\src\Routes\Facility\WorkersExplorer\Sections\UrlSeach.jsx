import { WorkersData, WorkersExplorerStore } from "../../../../data";

export default function UrlSearch() {
  const [WorkersExplorerState, setWorkersExplorerState] =
    WorkersExplorerStore.useStore();
  const workersData = WorkersData.useStore({ setter: false });

  const renderWorkerPath = () => {
    if (typeof WorkersExplorerState.worker !== "number") return null;

    const worker =
      workersData[WorkersExplorerState.type][WorkersExplorerState.worker];
    const WorkerInfo = `${worker.name.join(" ")} (${worker.cin})`;

    return (
      <>
        <ChevronSeparator />
        <PathSegment label={WorkerInfo} />
      </>
    );
  };

  const renderWorkerTypePath = () => {
    if (typeof WorkersExplorerState.type !== "string") return;

    const worlerType = WorkersExplorerState.type;

    return (
      <>
        <ChevronSeparator />
        <PathSegment
          label={worlerType}
          onClick={() => {
            setWorkersExplorerState({
              ...WorkersExplorerState,
              worker: null,
            });
          }}
        />
        {renderWorkerPath()}
      </>
    );
  };

  return (
    <div className="w-full h-12 px-12 flex items-center border-b border-gray-200 gap-2 text-base bg-white shadow-sm">
      <PathSegment
        label="Patients"
        icon="fa-solid fa-folder"
        highlight
        onClick={() => {
          setWorkersExplorerState({
            type: null,
            worker: null,
          });
        }}
      />
      {renderWorkerTypePath()}
    </div>
  );
}

// Reusable component for path segments
const PathSegment = ({
  label,
  icon,
  highlight = false,
  onClick = () => {},
}) => (
  <div
    onClick={onClick}
    className={`flex items-center cursor-pointer ${
      highlight ? "text-blue-600" : "bg-blue-50 px-3 py-1 rounded-full"
    }`}
  >
    {icon && <i className={`${icon} mr-2`}></i>}
    <span
      className={`${highlight ? "font-medium" : "font-medium text-gray-700"}`}
    >
      {label}
    </span>
  </div>
);

// Reusable component for chevron separators
const ChevronSeparator = () => (
  <i className="fa-solid fa-chevron-right text-gray-400 mx-1"></i>
);
