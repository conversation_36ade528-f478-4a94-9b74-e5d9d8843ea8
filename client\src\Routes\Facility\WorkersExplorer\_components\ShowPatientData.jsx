import { formatDate } from "../../../../utils/dateFormater";

export default function ShowPatientData({ patient }) {
  if (!patient) return null;

  const birthDate = patient.BirthDay
    ? formatDate(patient.BirthDay)
    : "Not specified";

  const genderData = {
    M: { icon: "mars", color: "blue-600", label: "Male" },
    F: { icon: "venus", color: "pink-600", label: "Female" },
    default: { icon: "genderless", color: "gray-500", label: "Not specified" },
  };

  const { icon, color, label } = patient.gender
    ? genderData[patient.gender] || genderData.default
    : genderData.default;

  const contactInfo = patient.email
    ? { icon: "envelope", text: patient.email }
    : patient.phone
    ? { icon: "phone", text: patient.phone }
    : { icon: "circle-question", text: "Not specified" };

  return (
    <div className="pb-4">
      <h2 className="text-2xl font-bold text-gray-900 mb-3 flex items-center">
        <i className="fas fa-user-injured text-blue-600 mr-2"></i>
        Patient:{" "}
        <span className="text-blue-700 ml-1">{patient.name.join(" ")}</span>
      </h2>

      <div className="bg-gray-100 p-4 rounded-lg border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Gender */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="fas fa-venus-mars text-gray-500 mr-2"></i>
              Gender:
            </h4>
            <p className="text-gray-900 flex items-center">
              <i className={`fas fa-${icon} text-${color} mr-2`}></i>
              {label}
            </p>
          </div>

          {/* Date of Birth */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="fas fa-birthday-cake text-gray-500 mr-2"></i>
              Date of Birth:
            </h4>
            <p className="text-gray-900">
              {birthDate === "Not specified" ? (
                <span className="flex items-center">
                  <i className="fas fa-circle-question text-gray-500 mr-2"></i>
                  {birthDate}
                </span>
              ) : (
                birthDate
              )}
            </p>
          </div>

          {/* CIN */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="fas fa-id-card text-gray-500 mr-2"></i>
              CIN:
            </h4>
            <p className="text-gray-900">
              {patient.cin ? (
                patient.cin
              ) : (
                <span className="flex items-center">
                  <i className="fas fa-circle-question text-gray-500 mr-2"></i>
                  Not specified
                </span>
              )}
            </p>
          </div>

          {/* Contact */}
          <div className="bg-white p-3 rounded-lg shadow-sm">
            <h4 className="font-medium text-gray-800 flex items-center mb-1">
              <i className="fas fa-address-book text-gray-500 mr-2"></i>
              Contact:
            </h4>
            <p className="text-gray-900 flex items-center">
              <i
                className={`fas fa-${contactInfo.icon} text-blue-500 mr-2`}
              ></i>
              {contactInfo.text}
            </p>
          </div>

          {/* Medical Notes */}
          {patient.folder.notes && (
            <div className="col-span-1 md:col-span-2 bg-white p-3 rounded-lg shadow-sm">
              <h4 className="font-medium text-gray-800 flex items-center mb-1">
                <i className="fas fa-file-medical text-gray-500 mr-2"></i>
                Medical Notes:
              </h4>
              <p className="text-gray-900 whitespace-pre-wrap bg-gray-50 p-3 rounded">
                {patient.folder.notes}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
