import { Router } from "express";
import { User } from "../../Models/User";
import { generateUserJWT } from "../../utils/jwt";
import { hashPassword } from "../../utils/passwordHash";
const router = Router();
router.post("/", async (req, res) => {
  const { email, password } = req.body;
  if (!email || !password) {
    res.status(400).json({ message: "Please provide email and password" });
    return;
  }
  const response = await User.findOne({
    email,
    password: hashPassword(password),
  }).lean();

  if (!response) {
    res.status(401).json({ message: "Invalid email or password" });
    return;
  }
  res.json({
    data: response,
    tokens: response.profiles.map((p) =>
      generateUserJWT({
        id: response._id.toString(),
        role: p.type,
        email: response.email,
      })
    ),
  });
});
export default router;
