import { ObjectId, Schema, model } from "mongoose";
import { BaseModel } from "./BaseModel";
import { ModelRefs } from "./ModelRefs";

export interface IVisit extends BaseModel {
  date: number;
  notes: string | null;
  doctor: ObjectId;
  is_deleted?: boolean;
}

const visitSchema = new Schema<IVisit>({
  date: { type: Number, required: true },
  notes: { type: String, default: null },
  doctor: {
    type: Schema.ObjectId,
    ref: ModelRefs.User.modelName,
    required: true,
  },
  createdAt: Number,
  is_deleted: { type: Boolean, default: false },
  updatedAt: Number,
});

export const Visit = model<IVisit>(ModelRefs.Visit.modelName, visitSchema);
