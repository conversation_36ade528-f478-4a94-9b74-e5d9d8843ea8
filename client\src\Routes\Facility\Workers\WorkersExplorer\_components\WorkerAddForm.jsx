import { useState } from "react";
import {
  TextField,
  Button,
  Box,
  Typography,
  MenuItem,
  Select,
  RadioGroup,
  FormControlLabel,
  Radio,
  Grid,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { PopUpElement, WorkersDataStore, LoadingBarStore, roles } from "../../../../../data";
import { createWorkerAPI } from "../../../../../api";
import toast from "react-hot-toast";

export default function WorkerAddForm() {
  const [formData, setFormData] = useState({
    cin: "",
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    gender: "M",
    birthDate: null,
    role: roles["doctor"],
    specialization: "",
    password: "",
  });

  const handleChange = (field) => (event) => {
    setFormData({
      ...formData,
      [field]: event.target.value,
    });
  };

  const handleDateChange = (date) => {
    setFormData({
      ...formData,
      birthDate: date,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    LoadingBarStore.setCurrent({ loading: true });

    try {
      // Convert birthDate to Unix timestamp
      const submitData = {
        ...formData,
        birthDate: formData.birthDate ? formData.birthDate.getTime() : null,
      };

      const [error, newWorker] = await createWorkerAPI(
        localStorage.getItem("token"),
        submitData
      );

      if (error) {
        toast.error(error?.response?.data?.error || "Failed to create worker");
        return;
      }

      const currentWorkers = WorkersDataStore.getCurrent();
      currentWorkers.worker.unshift(newWorker);
      WorkersDataStore.setCurrent({ ...currentWorkers });

      toast.success("Worker created successfully!");
      PopUpElement.setCurrent({ element: () => null });
    } catch (err) {
      console.error("Error creating worker:", err);
      toast.error("Failed to create worker");
    } finally {
      LoadingBarStore.setCurrent({ loading: false });
    }
  };

  const handleCancel = () => {
    PopUpElement.setCurrent({ element: () => null });
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      className="flex flex-col gap-4 p-6 bg-gray-50 rounded-lg border border-gray-200 w-[70%]"
    >
      <Typography variant="h6" component="h3" className="mb-2">
        Add New Worker
      </Typography>

      <Grid container spacing={2}>
        {/* Row 1 */}
        <Grid item xs={12} md={4}>
          <TextField
            label="CIN"
            fullWidth
            required
            value={formData.cin}
            onChange={handleChange("cin")}
            variant="outlined"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <TextField
            label="First Name"
            fullWidth
            required
            value={formData.firstName}
            onChange={handleChange("firstName")}
            variant="outlined"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <TextField
            label="Last Name"
            fullWidth
            required
            value={formData.lastName}
            onChange={handleChange("lastName")}
            variant="outlined"
          />
        </Grid>

        {/* Row 2 */}
        <Grid item xs={12} md={4}>
          <TextField
            label="Email"
            fullWidth
            required
            type="email"
            value={formData.email}
            onChange={handleChange("email")}
            variant="outlined"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <TextField
            label="Phone"
            fullWidth
            value={formData.phone}
            onChange={handleChange("phone")}
            variant="outlined"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="Birth Date"
              value={formData.birthDate}
              onChange={handleDateChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  fullWidth
                  required
                  variant="outlined"
                />
              )}
            />
          </LocalizationProvider>
        </Grid>

        {/* Row 3 */}
        <Grid item xs={12} md={4}>
          <RadioGroup
            row
            value={formData.gender}
            onChange={handleChange("gender")}
            className="flex gap-4"
          >
            <FormControlLabel value="M" control={<Radio />} label="Male" />
            <FormControlLabel value="F" control={<Radio />} label="Female" />
          </RadioGroup>
        </Grid>
        <Grid item xs={12} md={4}>
          <Select
            value={formData.role}
            label="Role"
            onChange={handleChange("role")}
            fullWidth
            required
            variant="outlined"
          >
            <MenuItem value="doctor">Doctor</MenuItem>
            <MenuItem value="admin">Admin</MenuItem>
          </Select>
        </Grid>
        <Grid item xs={12} md={4}>
          {formData.role === "doctor" && (
            <TextField
              label="Specialization"
              fullWidth
              value={formData.specialization}
              onChange={handleChange("specialization")}
              variant="outlined"
            />
          )}
        </Grid>
      </Grid>

      <TextField
        label="Password"
        type="password"
        fullWidth
        required
        value={formData.password}
        onChange={handleChange("password")}
        variant="outlined"
      />

      <div className="flex justify-end gap-2 mt-4">
        <Button
          variant="outlined"
          onClick={handleCancel}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          type="submit"
          className="bg-blue-500 hover:bg-blue-600"
        >
          Add Worker
        </Button>
      </div>
    </Box>
  );
}