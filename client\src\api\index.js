const API_BASE_URL = "http://localhost:4000";
import axios from "axios";

export const loginAPI = async ({ email, password }) => {
  try {
    return await axios.post(API_BASE_URL + "/login", {
      email,
      password,
    });
  } catch (e) {
    console.log(e);
    return null;
  }
};

export const addAppointementAPI = async ({
  title,
  description,
  cin,
  info,
  notes,
  date,
  token,
}) => {
  try {
    return [
      null,
      await axios.post(
        API_BASE_URL + "/appoinntements",
        {
          title: title,
          description: description,
          date: date,
          patientCIN: cin,
          additionalInfo: info,
          notes: notes,
        },
        {
          headers: {
            Authorization: token,
          },
        }
      ),
    ];
  } catch (e) {
    console.log(e);
    return [e, null];
  }
};

export const getAppointementForDateAPI = async ({ start, end, token }) => {
  try {
    return [
      null,
      await axios.get(API_BASE_URL + "/appoinntements", {
        headers: {
          Authorization: token,
        },
        params: {
          start: start,
          end: end,
        },
      }),
    ];
  } catch (e) {
    console.log(e);
    return [e, null];
  }
};

export const searchPatientByCINAPI = async ({
  cin,
  token,
  page_number = 0,
}) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/patients/search/cin/${cin}?page_number=${page_number}`,
      {
        headers: {
          Authorization: token,
        },
      }
    );
    return [null, response.data];
  } catch (e) {
    console.error("CIN search error:", e);
    return [e, null];
  }
};

export const searchTreatmentByTitleAPI = async (
  title,
  selectedPatient,
  token
) => {
  try {
    if (!title || title.length < 2) return ["error data", null];

    const patientId = selectedPatient ? selectedPatient : undefined;

    const response = await axios.get(
      API_BASE_URL + "/treatments/search/title",
      {
        params: { title, patientId },
        headers: {
          Authorization: token,
        },
      }
    );

    return [null, response.data];
  } catch (e) {
    console.error(e);
    return [e, null];
  }
};

export const createVisitAPI = async (visitData, treatementID, token) => {
  try {
    return [
      null,
      await axios.post(API_BASE_URL + `/visits/${treatementID}`, visitData, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      }),
    ];
  } catch (e) {
    console.error(e);
    return [e, null];
  }
};

export const createTreatementAPI = async (treatmentData, token) => {
  try {
    return [
      null,
      await axios.post(API_BASE_URL + `/treatments`, treatmentData, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      }),
    ];
  } catch (e) {
    console.error(e);
    return [e, null];
  }
};

export const createPatientAPI = async (patientData, token) => {
  try {
    return [
      null,
      await axios.post(API_BASE_URL + `/patients`, patientData, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      }),
    ];
  } catch (e) {
    console.error(e);
    return [e, null];
  }
};

export const getPatientTreatementByFolderId = async (id, token) => {
  try {
    if (!id || typeof id !== "string" || !token || typeof token !== "string")
      return ["id error or token error", null];
    const response = await axios.get(
      API_BASE_URL + "/treatments/search/folder/" + id,
      {
        headers: {
          Authorization: token,
        },
      }
    );

    return [null, response.data];
  } catch (e) {
    console.error(e);
    return [e, null];
  }
};

export const getTreatementVisitsByTreatementId = async (id, token) => {
  try {
    if (!id || typeof id !== "string" || !token || typeof token !== "string")
      return ["id error or token error", null];
    const response = await axios.get(API_BASE_URL + "/visits/" + id, {
      headers: {
        Authorization: token,
      },
    });

    return [null, response.data];
  } catch (e) {
    console.error(e);
    return [e, null];
  }
};

export const getMedicinByTitle = async (title, token) => {
  try {
    if (
      !title ||
      typeof title !== "string" ||
      !token ||
      typeof token !== "string"
    )
      return ["title error or token error", null];
    const response = await axios.get(API_BASE_URL + "/medecins/title", {
      params: { title },
      headers: {
        Authorization: token,
      },
    });

    return [null, response.data];
  } catch (e) {
    console.error(e);
    return [e, null];
  }
};

export const updateTreatementAPI = async (id, data, token) => {
  try {
    if (
      !id ||
      typeof id !== "string" ||
      !token ||
      typeof token !== "string" ||
      !data
    )
      return ["id error or token error or data error", null];
    const response = await axios.put(API_BASE_URL + `/treatments/${id}`, data, {
      headers: {
        Authorization: token,
      },
    });

    return [null, response.data];
  } catch (e) {
    return [e, null];
  }
};

export const updateVisitAPI = async (id, data, token) => {
  try {
    if (
      !id ||
      typeof id !== "string" ||
      !token ||
      typeof token !== "string" ||
      !data
    )
      return ["id error or token error or data error", null];
    const response = await axios.put(API_BASE_URL + `/visits/${id}`, data, {
      headers: {
        Authorization: token,
      },
    });

    return [null, response.data];
  } catch (e) {
    console.error(e);
    return [e, null];
  }
};

export const updateTreatementPrescriptionAPI = async (id, data, token) => {
  try {
    if (
      !id ||
      typeof id !== "string" ||
      !token ||
      typeof token !== "string" ||
      !data
    )
      return ["id error or token error or data error", null];
    const response = await axios.put(
      API_BASE_URL + `/treatments/prescriptions/${id}`,
      data,
      {
        headers: {
          Authorization: token,
        },
      }
    );

    return [null, response.data];
  } catch (e) {
    console.error(e);
    return [e, null];
  }
};

export const searchAppointementsAPI = async ({
  query,
  cin,
  description,
  title,
  token,
}) => {
  try {
    const params = {};
    if (cin) params.cin = cin;
    if (description) params.description = description;
    if (title) params.title = title;
    if (query) params.query = query;
    console.log(params);
    const response = await axios.get(API_BASE_URL + "/appoinntements/search", {
      headers: {
        Authorization: token,
      },
      params,
    });
    return [null, response.data];
  } catch (e) {
    console.error(e);
    return [e, null];
  }
};

// Doctor Dashboard APIs
export const getDashboardStatsAPI = async (token) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/doctors/stats`, {
      headers: {
        Authorization: token,
      },
    });
    return [null, response.data];
  } catch (e) {
    console.error("Dashboard stats error:", e);
    return [e, null];
  }
};

export const getRecentAppointmentsAPI = async (token, limit = 5) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/doctors/recent-appointments`,
      {
        headers: {
          Authorization: token,
        },
        params: { limit },
      }
    );
    return [null, response.data];
  } catch (e) {
    console.error("Recent appointments error:", e);
    return [e, null];
  }
};

export const getRecentPatientsAPI = async (token, limit = 5) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/doctors/recent-patients`,
      {
        headers: {
          Authorization: token,
        },
        params: { limit },
      }
    );
    return [null, response.data];
  } catch (e) {
    console.error("Recent patients error:", e);
    return [e, null];
  }
};

export const getMonthlyStatsAPI = async (
  token,
  year = new Date().getFullYear()
) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/doctors/monthly-stats`, {
      headers: {
        Authorization: token,
      },
      params: { year },
    });
    return [null, response.data];
  } catch (e) {
    console.error("Monthly stats error:", e);
    return [e, null];
  }
};

export const getDoctorProfileAPI = async (token) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/doctors/profile`, {
      headers: {
        Authorization: token,
      },
    });
    return [null, response.data];
  } catch (e) {
    console.error("Doctor profile error:", e);
    return [e, null];
  }
};

export const updateDoctorProfileAPI = async (token, profileData) => {
  try {
    const response = await axios.put(
      `${API_BASE_URL}/doctors/profile`,
      profileData,
      {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      }
    );
    return [null, response.data];
  } catch (e) {
    console.error("Update profile error:", e);
    return [e, null];
  }
};

export const getDoctorAppointmentsAPI = async (token, params = {}) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/doctors/appointments`, {
      headers: {
        Authorization: token,
      },
      params,
    });
    return [null, response.data];
  } catch (e) {
    console.error("Doctor appointments error:", e);
    return [e, null];
  }
};
export const searchPatientViaAdvancedOptionsAPI = async (token, options) => {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/patients/search/advanced`,
      {
        headers: {
          Authorization: token,
        },
        params: options,
      }
    );
    return [null, response.data];
  } catch (e) {
    console.error("patient search error:", e);
    return [e, null];
  }
};
