import { PatientsDataStore, visitsExplorerStore } from "../../../../../data";
import {
  Autocomplete,
  Box,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import { useFetchPatients } from "../../../../../utils/costumeHook";

export default function PatientSelector({
  formData,
  setFormData,
  showFields,
  setShowFields,
}) {
  const { loadMore, loading } = useFetchPatients();
  const patients = PatientsDataStore.useStore({ setter: false }).patient.map(
    (patient) => ({
      label: patient.cin,
      value: patient,
    })
  );

  const handleSelect = async (e, option) => {
    if (!option?.value) {
      setShowFields(true);
      setFormData((prev) => ({
        ...prev,
        patient: {
          cin: "",
          firstName: "",
          lastName: "",
          birthDate: "",
          gender: "",
          email: "",
          phone: "",
          _id: "",
        },
      }));
      return;
    }

    const patient = option.value;
    setFormData((prev) => ({
      ...prev,
      patient: {
        cin: patient.cin,
        firstName: patient.name?.[0] || "",
        lastName: patient.name?.[1] || "",
        birthDate: patient.BirthDay ? patient.BirthDay : Date.now(),
        gender: patient.gender || "",
        email: patient.email || "",
        phone: patient.phone || "",
        _id: patient._id,
      },
    }));
    const index = patients.findIndex((p) => p.value._id == option.value._id);

    visitsExplorerStore.setCurrent({
      patient: index,
      treatement: undefined,
      visit: undefined,
    });

    setShowFields(false);
  };

  return (
    <Box className="flex flex-col gap-4 p-6 bg-gray-50 rounded-lg border border-gray-200">
      <Typography variant="h6" component="h3" className="!mb-4">
        Patient Information
      </Typography>

      <Box className="flex gap-4 flex-wrap">
        <Autocomplete
          className="min-w-[250px] flex-1"
          options={patients}
          onChange={handleSelect}
          onInputChange={async (e, text) => {
            if (
              patients.filter((p) => {
                return p.value.cin.includes(text);
              }).length <= 4
            ) {
              await loadMore(text);
            }
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Search by CIN"
              placeholder="Enter patient CIN"
              variant="outlined"
              fullWidth
            />
          )}
          loading={loading}
          clearOnEscape
        />
      </Box>

      <Box className="flex gap-4 flex-wrap">
        <TextField
          disabled={!showFields}
          className="min-w-[180px] flex-1"
          label="First Name"
          value={formData.patient.firstName || ""}
          onChange={(e) => {
            formData.patient.firstName = e.target.value;
            setFormData({ ...formData });
          }}
          variant="outlined"
        />
        <TextField
          disabled={!showFields}
          className="min-w-[180px] flex-1"
          label="Last Name"
          value={formData.patient.lastName || ""}
          onChange={(e) => {
            formData.patient.lastName = e.target.value;
            setFormData({ ...formData });
          }}
          variant="outlined"
        />
      </Box>

      <Box className="flex gap-4 flex-wrap">
        <TextField
          disabled={!showFields}
          className="min-w-[180px] flex-1"
          label="Birth Date"
          type="date"
          InputLabelProps={{ shrink: true }}
          value={
            new Date(formData.patient.birthDate || Date.now())
              .toISOString()
              .split("T")[0]
          }
          onChange={(e) => {
            formData.patient.birthDate = e.target.value;
            setFormData({ ...formData });
          }}
          variant="outlined"
        />
        <Select
          disabled={!showFields}
          className="min-w-[180px] flex-1"
          labelId="gender-label"
          value={formData.patient.gender || ""}
          onChange={(e) => {
            formData.patient.gender = e.target.value;
            setFormData({ ...formData });
          }}
          displayEmpty
          label="Gender"
        >
          <MenuItem value="">
            <em>Select Gender</em>
          </MenuItem>
          <MenuItem value="M">Male</MenuItem>
          <MenuItem value="F">Female</MenuItem>
        </Select>
      </Box>

      <Box className="flex gap-4 flex-wrap">
        <TextField
          disabled={!showFields}
          className="min-w-[180px] flex-1"
          label="Email"
          type="email"
          value={formData.patient.email || ""}
          onChange={(e) => {
            formData.patient.email = e.target.value;
            setFormData({ ...formData });
          }}
          variant="outlined"
        />
        <TextField
          disabled={!showFields}
          className="min-w-[180px] flex-1"
          label="Phone Number"
          value={formData.patient.phone || ""}
          onChange={(e) => {
            formData.patient.phone = e.target.value;
            setFormData({ ...formData });
          }}
          variant="outlined"
        />
      </Box>
    </Box>
  );
}
